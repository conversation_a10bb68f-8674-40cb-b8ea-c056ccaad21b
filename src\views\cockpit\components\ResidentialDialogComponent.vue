<template>
  <div v-if="visible" class="dialog-container" @click.self="closeDialog">
    <div class="dialog-content">
      <!-- 科技风边框装饰 -->
      <div class="tech-border">
        <div class="corner corner-tl"></div>
        <div class="corner corner-tr"></div>
        <div class="corner corner-bl"></div>
        <div class="corner corner-br"></div>
        <div class="edge edge-top"></div>
        <div class="edge edge-bottom"></div>
        <div class="edge edge-left"></div>
        <div class="edge edge-right"></div>
      </div>
      
      <!-- 关闭按钮 -->
      <div class="close-btn" @click="closeDialog">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
        </svg>
      </div>
      
      <!-- 标题区域 -->
      <div class="dialog-header">
        <div class="title-line"></div>
        <h2 class="dialog-title">
          <el-icon class="title-icon"><HomeFilled /></el-icon>
          {{ residentialData.name || '小区详情' }}
        </h2>
        <div class="title-line"></div>
      </div>

      <!-- Tab导航 -->
      <div class="tab-nav">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab-item', { active: activeTab === index }]"
          @click="activeTab = index">
          <span class="tab-text">{{ tab }}</span>
          <div class="tab-indicator"></div>
        </div>
      </div>

      <!-- Tab内容区域 -->
      <div class="tab-content">
        <!-- 基本信息 Tab -->
        <ResidentialBasicInfoTab v-show="activeTab === 0" :residential-data="residentialData" />

        <!-- 物业信息 Tab -->
        <ResidentialPropertyTab v-show="activeTab === 1" :residential-data="residentialData" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import ResidentialBasicInfoTab from './residential-tabs/ResidentialBasicInfoTab.vue';
import ResidentialPropertyTab from './residential-tabs/ResidentialPropertyTab.vue';
import { HomeFilled } from "@element-plus/icons-vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  residentialData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['close']);

// Tab相关
const tabs = ['基本信息'];
const activeTab = ref(0);

// 关闭对话框
const closeDialog = () => {
  // 重置到基本信息Tab
  activeTab.value = 0;
  emit('close');
};




</script>

<style scoped>
/* 对话框容器 */
.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background: radial-gradient(ellipse at center, rgba(20, 50, 120, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 对话框主体 */
.dialog-content {
  position: relative;
  width: 1400px;
  height: 800px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.95) 0%,
    rgba(25, 55, 125, 0.98) 50%,
    rgba(20, 50, 120, 0.95) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
  border-radius: 12px;
  padding: 30px;
  color: #fff;
  display: flex;
  flex-direction: column;
  box-shadow:
    0 0 50px rgba(0, 149, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from { 
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
  }
  to { 
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* 科技风边框装饰 */
.tech-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: 12px;
  overflow: hidden;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #00feff;
}

.corner-tl {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.corner-tr {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.corner-bl {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.corner-br {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
}

.edge {
  position: absolute;
  background: linear-gradient(90deg, transparent, #00feff, transparent);
  opacity: 0.6;
}

.edge-top, .edge-bottom {
  height: 1px;
  left: 50px;
  right: 50px;
}

.edge-top { top: 10px; }
.edge-bottom { bottom: 10px; }

.edge-left, .edge-right {
  width: 1px;
  top: 50px;
  bottom: 50px;
  background: linear-gradient(180deg, transparent, #00feff, transparent);
}

.edge-left { left: 10px; }
.edge-right { right: 10px; }

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #00feff;
  cursor: pointer;
  background: rgba(20, 50, 120, 0.8);
  border: 1px solid rgba(0, 149, 255, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
  z-index: 10;
}

.close-btn:hover {
  background: rgba(0, 149, 255, 0.2);
  border-color: #00feff;
  box-shadow: 0 0 15px rgba(0, 254, 255, 0.3);
  transform: scale(1.05);
}

/* 标题区域 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  gap: 15px;
}

.title-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00feff, transparent);
  opacity: 0.6;
}

.dialog-title {
  font-size: 28px;
  font-weight: 600;
  color: #00feff;
  text-shadow: 0 0 10px rgba(0, 254, 255, 0.5);
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.title-icon {
  font-size: 20px;
}

/* Tab导航 */
.tab-nav {
  display: flex;
  gap: 2px;
  margin-bottom: 25px;
  background: rgba(20, 50, 120, 0.5);
  border-radius: 8px;
  padding: 4px;
  border: 1px solid rgba(0, 149, 255, 0.2);
}

.tab-item {
  position: relative;
  padding: 12px 24px;
  cursor: pointer;
  border-radius: 6px;
  background: transparent;
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
  overflow: hidden;
}

.tab-text {
  position: relative;
  z-index: 2;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #00feff;
  text-shadow: 0 0 8px rgba(0, 254, 255, 0.5);
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00feff, transparent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tab-item.active .tab-indicator {
  transform: scaleX(1);
}

.tab-item.active {
  background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.1) 0%,
    rgba(0, 254, 255, 0.05) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
}

.tab-item:hover:not(.active) {
  background: rgba(0, 149, 255, 0.05);
}

.tab-item:hover:not(.active) .tab-text {
  color: rgba(255, 255, 255, 0.9);
}

/* Tab内容区域 */
.tab-content {
  flex: 1;
  overflow: hidden;
  background: rgba(20, 50, 120, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 149, 255, 0.1);
}

.tab-pane {
  height: 100%;
  overflow-y: auto;
}
</style>
