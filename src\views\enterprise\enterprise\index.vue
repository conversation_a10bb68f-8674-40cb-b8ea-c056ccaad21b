<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="楼宇名称" prop="buildingId" label-width="170">
        <el-select v-model="queryParams.buildingId" placeholder="请选择楼宇" clearable style="width: 130px">
          <el-option v-for="dict in buildinglist" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在楼层" prop="floor" label-width="170">
        <el-input v-model="queryParams.floor" placeholder="请输入所在楼层" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="uscCode" label-width="170">
        <el-input v-model="queryParams.uscCode" placeholder="请输入统一社会信用代码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="企业名称" prop="name" label-width="170">
        <el-input v-model="queryParams.name" placeholder="请输入企业名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status" label-width="170">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 130px">
          <el-option v-for="dict in enterprise_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="法人" prop="legalPerson" label-width="170">
        <el-input v-model="queryParams.legalPerson" placeholder="请输入法人" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="联系电话" prop="concatMobile" label-width="170">
        <el-input v-model="queryParams.concatMobile" placeholder="请输入联系电话" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="地址" prop="address" label-width="170">
        <el-input v-model="queryParams.address" placeholder="请输入地址" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="成立日期" prop="openDate">
        <el-date-picker
          clearable
          v-model="queryParams.openDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择成立日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['enterprise:enterprise:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['enterprise:enterprise:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['enterprise:enterprise:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['enterprise:enterprise:import']"
          >导入</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['enterprise:enterprise:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="enterpriseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="80" align="center" />
      <el-table-column label="序列" align="center" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="主键" align="center" prop="id" v-if="false" />
      <el-table-column label="楼宇名称" align="center" prop="buildingId" width="100">
        <template #default="scope"> <dict-tag :options="buildinglist" :value="scope.row.buildingId" /> </template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" align="center" prop="uscCode" width="200" />
      <el-table-column label="企业名称" align="center" prop="name" width="400" />
      <el-table-column label="状态" align="center" prop="status" width="150">
        <template #default="scope"> <dict-tag :options="enterprise_status" :value="scope.row.status" /> </template>
      </el-table-column>
      <el-table-column label="类别" align="center" prop="category" width="150" />
      <el-table-column label="法人" align="center" prop="legalPerson" width="130" />
      <el-table-column label="联系电话" align="center" prop="concatMobile" width="130" />
      <el-table-column label="地址" align="center" prop="address" width="500" />
      <el-table-column label="成立日期" align="center" prop="openDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.openDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
<el-table-column label="注册资金(万元)" align="center" prop="registeredCapital" width="130"/>
<el-table-column label="营收(万元)" align="center" prop="revenue" width="130"/>
<el-table-column label="税收(万元)" align="center" prop="taxation" width="130"/>
<el-table-column label="行业分类" align="center" prop="industryClassification" width="150"/>
<el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="130" fixed="right">

        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['enterprise:enterprise:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['enterprise:enterprise:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改企业信息对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="enterpriseRef" :model="form" :rules="rules" label-width="100px">
<el-row>
<el-col :span="25">
<el-form-item label="楼宇" prop="buildingId" label-width="170">
   <el-select v-model="form.buildingId" placeholder="请选择楼宇" style="width:280px">
      <el-option
      v-for="dict in buildinglist"
      :key="dict.value"
      :label="dict.label"
      :value="parseInt(dict.value)"
      ></el-option>
          </el-select>
</el-form-item>
</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="uscCode" label-width="170">
              <el-input v-model="form.uscCode" placeholder="请输入统一社会信用代码" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业名称" prop="name" label-width="170">
              <el-input v-model="form.name" placeholder="请输入企业名称" style="width: 340px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status" label-width="170">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 280px">
                <el-option
                  v-for="dict in enterprise_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类别" prop="category" label-width="170">
              <el-input v-model="form.category" placeholder="请输入类别" style="width: 340px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人" prop="legalPerson" label-width="170">
              <el-input v-model="form.legalPerson" placeholder="请输入法人" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="concatUser" label-width="170">
              <el-input v-model="form.concatUser" placeholder="请输入联系人" style="width: 340px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="concatMobile" label-width="170">
              <el-input v-model="form.concatMobile" placeholder="请输入联系电话" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行业分类" prop="industryClassification" label-width="170">
              <el-input v-model="form.industryClassification" placeholder="请输入行业分类" style="width: 340px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-row>
<el-col :span="12">
<el-form-item label="注册资金（万元）" prop="registeredCapital" label-width="170">
   <el-input v-model="form.registeredCapital" placeholder="请输入注册资金"  style="width:280px" />
</el-form-item>
</el-col>
<el-col :span="12">
<el-form-item label="成立日期" prop="openDate" label-width="170">
       <el-date-picker clearable
            v-model="form.openDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择成立日期" style="width:340px">
       </el-date-picker>
  </el-form-item>
</el-col>
</el-row>

<el-row>
<el-col :span="12">
<el-form-item label="营收（万元）" prop="revenue" label-width="170">
   <el-input v-model="form.revenue" placeholder="请输入营收"  style="width:280px" />
</el-form-item>
</el-col>
<el-col :span="12">
<el-form-item label="税收（万元）" prop="taxation" label-width="170">
   <el-input v-model="form.taxation" placeholder="请输入税收"  style="width:340px" />
</el-form-item>
            </el-col>
          </el-row>
          <el-col :span="25">
            <el-form-item label="地址" prop="address" label-width="170">
              <el-input v-model="form.address" placeholder="请输入地址" style="width: 790px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="25">
            <el-form-item label="业务范围" prop="businessScope" label-width="170">
              <el-input v-model="form.businessScope" type="textarea" placeholder="请输入内容" style="width: 790px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="enterprise">
import { getToken } from "@/utils/auth";
import { listEnterprise, getEnterprise, delEnterprise, addEnterprise, updateEnterprise, writeDataEnterprise } from "@/api/enterprise/enterprise";
const { proxy } = getCurrentInstance();
const { enterprise_status, buildinglist } = proxy.useDict("enterprise_status", "buildinglist");
const enterpriseList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeOpenDate = ref([]);
const daterangeCreateTime = ref([]);
const daterangeUpdateTime = ref([]);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    buildingId: null,
    uscCode: null,
    name: null,
    status: null,
    legalPerson: null,
    concatMobile: null,
    address: null,
    floor: null,
    openDate: null,
  },
  rules: ref({}),
});
const { queryParams, form, rules } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/enterprise/enterprise/importData",
}); /** 查询企业信息列表 */
function getList() {
  loading.value = true;
  listEnterprise(queryParams.value).then((response) => {
    enterpriseList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    buildingId: null,
    uscCode: null,
    name: null,
    status: null,
    category: null,
    legalPerson: null,
    concatUser: null,
    concatMobile: null,
    address: null,
    openDate: null,
    registeredCapital: null,
    businessScope: null,
    industryClassification: null,
    remark: null,
  };
  proxy.resetForm("enterpriseRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加企业信息";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getEnterprise(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改企业信息";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["enterpriseRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateEnterprise(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEnterprise(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除选中的数据，此次将删除" + _ids.length + "条数据？")
    .then(function () {
      return delEnterprise(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "enterprise/enterprise/export",
    {
      ...queryParams.value,
    },
    `enterprise_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "企业信息导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("enterprise/enterprise/downTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);

  if(response.code !== 200){
    proxy.$modal.msgError(response.msg);
    return;
  }

  // 显示确认窗体，内容是response.msg
  proxy.$confirm(
    `<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${response.msg}</div>`,
    "导入结果确认",
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "info"
    }
  ).then(() => {
    return writeDataEnterprise();
  }).then(() => {
    proxy.$modal.msgSuccess("数据写入成功");
    getList();
  }).catch((error) => {
    if (error !== 'cancel') {
      console.error('数据写入失败:', error);
      proxy.$modal.msgError("数据写入失败");
    }
  });
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
getList();
</script>
