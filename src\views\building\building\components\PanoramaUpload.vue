<template>
  <div class="component-upload-image">
    <el-upload multiple :action="uploadImgUrl" list-type="picture-card" :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload" :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed"
      ref="imageUpload" :before-remove="handleDelete" :show-file-list="true" :headers="headers" :file-list="fileList"
      :on-preview="handlePictureCardPreview" :class="{ hide: fileList.length >= limit }" :name="name">
      <el-icon class="avatar-uploader-icon">
        <plus />
      </el-icon>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的全景图文件
    </div>

    <el-dialog v-model="dialogVisible" title="全景图预览" width="800px" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </el-dialog>
  </div>
</template>

<script setup name="PanoramaUpload">
import { getToken } from "@/utils/auth";
import { isExternal } from "@/utils/validate";

const props = defineProps({
  // 数组格式：[{ name: string, url: string, id: string|number, size: number }]
  modelValue: {
    type: Array,
    default: () => []
  },
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 10, // 全景图通常较大，默认10MB
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 自定义上传接口地址
  uploadUrl: {
    type: String,
    default: "/common/upload"
  },
  // 自定义上传字段名称
  name: {
    type: String,
    default: "file"
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);
const uploadImgUrl = computed(() => baseUrl + props.uploadUrl);

// 监听 modelValue 变化，处理数组格式数据
watch(() => props.modelValue, val => {
  if (val && Array.isArray(val)) {
    // 处理数组格式的数据
    fileList.value = val.map(item => {
      // 确保每个项目都有必要的属性
      const fileItem = {
        name: item.name || '',
        url: item.url || item.webUrl || '',
        webUrl: item.webUrl || '',
        id: item.id || '',
        size: item.size || 0
      };

      // 处理URL路径
      if (fileItem.url && fileItem.url.indexOf(baseUrl) === -1 && !isExternal(fileItem.url)) {
        fileItem.url = baseUrl + fileItem.url;
        fileItem.name = fileItem.name || fileItem.url;
      }

      return fileItem;
    });
  } else {
    fileList.value = [];
  }
}, { deep: true, immediate: true });

// 上传前验证
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(`文件格式不正确，请上传${props.fileType.join("/")}图片格式文件!`);
    return false;
  }
  if (file.name.includes(',')) {
    proxy.$modal.msgError('文件名不正确，不能包含英文逗号!');
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传全景图大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading("正在上传全景图，请稍候...");
  number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调 - 优化处理逻辑
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    res.data.forEach(item => {
      uploadList.value.push({
        name: item.name || file.name,
        url: item.webUrl,
        webUrl: item.webUrl,
        id: item.id || Date.now() + Math.random(),
        size: item.size || file.size
      });
    });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg || '上传失败');
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map(f => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    // 直接发射数组格式，不进行字符串转换
    emit("update:modelValue", fileList.value);
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    // 发射数组格式的数据，移除字符串转换
    emit("update:modelValue", fileList.value);
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传全景图失败");
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
</style>
