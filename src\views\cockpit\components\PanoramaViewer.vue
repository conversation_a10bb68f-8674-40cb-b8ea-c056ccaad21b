<template>
  <div class="panorama-viewer-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载全景图片...</p>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <el-icon class="error-icon"><Warning /></el-icon>
      <p class="error-text">{{ error }}</p>
      <button @click="retryLoad" class="retry-btn">重试</button>
    </div>

    <!-- 全景查看器容器 -->
    <div ref="viewerContainer" class="viewer-container" :class="{ 'viewer-hidden': loading || error }"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Viewer } from '@photo-sphere-viewer/core'
import '@photo-sphere-viewer/core/index.css'
import { Warning } from "@element-plus/icons-vue"

// Props定义
const props = defineProps({
  panoramaUrl: {
    type: String,
    required: true,
    default: ''
  }
})

// 响应式数据
const viewerContainer = ref(null)
const viewer = ref(null)
const loading = ref(false)
const error = ref('')

// 初始化全景查看器
const initViewer = async () => {
  if (!props.panoramaUrl || !viewerContainer.value) {
    return
  }

  try {
    loading.value = true
    error.value = ''

    // 销毁现有的查看器实例
    if (viewer.value) {
      viewer.value.destroy()
      viewer.value = null
    }

    // 创建新的查看器实例
    viewer.value = new Viewer({
      container: viewerContainer.value,
      panorama: props.panoramaUrl
    })

    // 监听加载完成事件
    viewer.value.addEventListener('ready', () => {
      loading.value = false
    })

    // 监听错误事件
    viewer.value.addEventListener('panorama-load-error', () => {
      error.value = '全景图片加载失败'
      loading.value = false
    })

  } catch (err) {
    error.value = '全景查看器初始化失败'
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  error.value = ''
  initViewer()
}

// 销毁查看器
const destroyViewer = () => {
  if (viewer.value) {
    viewer.value.destroy()
    viewer.value = null
  }
}

// 监听panoramaUrl变化
watch(() => props.panoramaUrl, (newUrl) => {
  if (newUrl) {
    initViewer()
  }
})

// 组件挂载
onMounted(() => {
  if (props.panoramaUrl) {
    initViewer()
  }
})

// 组件卸载
onUnmounted(() => {
  destroyViewer()
})
</script>

<style scoped>
.panorama-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%);
}

.viewer-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.viewer-hidden {
  opacity: 0;
  pointer-events: none;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(20, 50, 120, 0.9);
  z-index: 10;
  border-radius: 8px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 254, 255, 0.3);
  border-top: 3px solid #00feff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-text {
  color: #00feff;
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(20, 50, 120, 0.9);
  z-index: 10;
  border-radius: 8px;
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.error-text {
  color: #ff6b6b;
  font-size: 14px;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.retry-btn {
  padding: 8px 20px;
  background: linear-gradient(135deg, #00feff 0%, #0095ff 100%);
  border: none;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #0095ff 0%, #00feff 100%);
  transform: translateY(-1px);
}
</style>
